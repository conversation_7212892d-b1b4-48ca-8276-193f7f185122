import React, { useState, useEffect } from 'react';
import { useToast } from '../../hooks/useToast';
import { useAuthStore } from '../../store/authStore';
import { apiClient } from '../../services/api';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';

interface OrderItem {
  id: number;
  product_id: number;
  quantity: number;
  unit_price: number;
  price_at_sale: number;
  product_name?: string;
}

interface Order {
  id: number;
  customer_name?: string;
  payment_method: string;
  cashier_id: number;
  total_amount: number;
  status: string;
  created_at: string;
  updated_at: string;
  items?: OrderItem[];
}

interface UpdateOrderRequest {
  customer_name?: string;
  payment_method: 'cash' | 'card' | 'mobile';
  cashier_id: number;
  status: 'pending' | 'completed' | 'cancelled' | 'refunded';
}

const OrdersManagement: React.FC = () => {
  const { user } = useAuthStore();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);
  const [showUpdateForm, setShowUpdateForm] = useState(false);
  const [updateFormData, setUpdateFormData] = useState<UpdateOrderRequest>({
    customer_name: '',
    payment_method: 'cash',
    cashier_id: 0,
    status: 'pending'
  });
  const [pagination] = useState({
    skip: 0,
    limit: 100,
    total: 0
  });

  const { showError, showSuccess } = useToast();

  // Check if user has permission to access orders (admin or manager only)
  const hasOrdersAccess = user?.role === 'admin' || user?.role === 'manager';

  if (!hasOrdersAccess) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <svg className="h-16 w-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Access Denied</h2>
          <p className="text-gray-600 dark:text-gray-400">
            Only administrators and managers can access the Orders Management page.
          </p>
        </div>
      </div>
    );
  }

  useEffect(() => {
    fetchOrders();
  }, [pagination.skip, pagination.limit]);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get<Order[]>(`/sales/orders?skip=${pagination.skip}&limit=${pagination.limit}`);
      setOrders(response);
    } catch (error) {
      console.error('Failed to fetch orders:', error);
      showError('Failed to load orders');
    } finally {
      setLoading(false);
    }
  };

  const fetchOrderDetails = async (orderId: number) => {
    try {
      const order = await apiClient.get<Order>(`/sales/orders/${orderId}`);
      setSelectedOrder(order);
      setShowOrderDetails(true);
    } catch (error) {
      console.error('Failed to fetch order details:', error);
      showError('Failed to load order details');
    }
  };

  const handleUpdateOrder = async () => {
    if (!selectedOrder) return;

    try {
      const updatedOrder = await apiClient.put<Order>(`/sales/orders/${selectedOrder.id}`, updateFormData);
      
      // Update the order in the list
      setOrders(orders.map(order => 
        order.id === selectedOrder.id ? updatedOrder : order
      ));
      
      setSelectedOrder(updatedOrder);
      setShowUpdateForm(false);
      showSuccess('Order updated successfully');
    } catch (error) {
      console.error('Failed to update order:', error);
      showError('Failed to update order');
    }
  };

  const handleRefundOrder = async (orderId: number) => {
    if (!confirm('Are you sure you want to refund this order? This action cannot be undone.')) {
      return;
    }

    try {
      await apiClient.post(`/sales/orders/${orderId}/refund`);
      
      // Update the order status in the list
      setOrders(orders.map(order => 
        order.id === orderId ? { ...order, status: 'refunded' } : order
      ));
      
      if (selectedOrder && selectedOrder.id === orderId) {
        setSelectedOrder({ ...selectedOrder, status: 'refunded' });
      }
      
      showSuccess('Order refunded successfully. Inventory has been updated.');
    } catch (error) {
      console.error('Failed to refund order:', error);
      showError('Failed to refund order');
    }
  };

  const openUpdateForm = (order: Order) => {
    setSelectedOrder(order);
    setUpdateFormData({
      customer_name: order.customer_name || '',
      payment_method: order.payment_method as 'cash' | 'card' | 'mobile',
      cashier_id: order.cashier_id,
      status: order.status as 'pending' | 'completed' | 'cancelled' | 'refunded'
    });
    setShowUpdateForm(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20';
      case 'cancelled':
        return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20';
      case 'refunded':
        return 'text-purple-600 bg-purple-100 dark:text-purple-400 dark:bg-purple-900/20';
      default:
        return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20';
    }
  };

  if (loading && orders.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading orders...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Orders Management</h1>
          <p className="text-muted-foreground mt-1">
            View and manage all sales orders
          </p>
        </div>
        <Button onClick={fetchOrders} disabled={loading}>
          {loading ? 'Refreshing...' : 'Refresh Orders'}
        </Button>
      </div>

      {/* Orders List */}
      <Card title="Orders List">
        {orders.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="text-left py-3 px-2">Order ID</th>
                  <th className="text-left py-3 px-2">Customer</th>
                  <th className="text-left py-3 px-2">Payment Method</th>
                  <th className="text-right py-3 px-2">Total Amount</th>
                  <th className="text-center py-3 px-2">Status</th>
                  <th className="text-left py-3 px-2">Created Date</th>
                  <th className="text-center py-3 px-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                {orders.map((order) => (
                  <tr key={order.id} className="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="py-3 px-2 font-medium">#{order.id}</td>
                    <td className="py-3 px-2">{order.customer_name || 'Walk-in Customer'}</td>
                    <td className="py-3 px-2 capitalize">{order.payment_method}</td>
                    <td className="py-3 px-2 text-right font-medium">${order.total_amount.toFixed(2)}</td>
                    <td className="py-3 px-2 text-center">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                      </span>
                    </td>
                    <td className="py-3 px-2">{new Date(order.created_at).toLocaleString()}</td>
                    <td className="py-3 px-2">
                      <div className="flex items-center justify-center space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => fetchOrderDetails(order.id)}
                        >
                          View
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => openUpdateForm(order)}
                        >
                          Edit
                        </Button>
                        {order.status === 'completed' && (
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleRefundOrder(order.id)}
                          >
                            Refund
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500 dark:text-gray-400">No orders found</p>
          </div>
        )}
      </Card>

      {/* Order Details Modal */}
      {showOrderDetails && selectedOrder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold">Order Details - #{selectedOrder.id}</h2>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowOrderDetails(false)}
              >
                Close
              </Button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Customer</p>
                  <p className="text-gray-900 dark:text-white">{selectedOrder.customer_name || 'Walk-in Customer'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Payment Method</p>
                  <p className="text-gray-900 dark:text-white capitalize">{selectedOrder.payment_method}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Status</p>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedOrder.status)}`}>
                    {selectedOrder.status.charAt(0).toUpperCase() + selectedOrder.status.slice(1)}
                  </span>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Total Amount</p>
                  <p className="text-gray-900 dark:text-white font-bold">${selectedOrder.total_amount.toFixed(2)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Created Date</p>
                  <p className="text-gray-900 dark:text-white">{new Date(selectedOrder.created_at).toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Cashier ID</p>
                  <p className="text-gray-900 dark:text-white">{selectedOrder.cashier_id}</p>
                </div>
              </div>

              {selectedOrder.items && selectedOrder.items.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium mb-2">Order Items</h3>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b border-gray-200 dark:border-gray-700">
                          <th className="text-left py-2">Product ID</th>
                          <th className="text-right py-2">Quantity</th>
                          <th className="text-right py-2">Unit Price</th>
                          <th className="text-right py-2">Total</th>
                        </tr>
                      </thead>
                      <tbody>
                        {selectedOrder.items.map((item) => (
                          <tr key={item.id} className="border-b border-gray-100 dark:border-gray-800">
                            <td className="py-2">{item.product_name || `Product ${item.product_id}`}</td>
                            <td className="py-2 text-right">{item.quantity}</td>
                            <td className="py-2 text-right">${item.price_at_sale.toFixed(2)}</td>
                            <td className="py-2 text-right">${(item.quantity * item.price_at_sale).toFixed(2)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Update Order Modal */}
      {showUpdateForm && selectedOrder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold">Update Order - #{selectedOrder.id}</h2>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowUpdateForm(false)}
              >
                Cancel
              </Button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Customer Name
                </label>
                <input
                  type="text"
                  value={updateFormData.customer_name}
                  onChange={(e) => setUpdateFormData(prev => ({ ...prev, customer_name: e.target.value }))}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="Walk-in Customer"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Payment Method
                </label>
                <select
                  value={updateFormData.payment_method}
                  onChange={(e) => setUpdateFormData(prev => ({ ...prev, payment_method: e.target.value as 'cash' | 'card' | 'mobile' }))}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="cash">Cash</option>
                  <option value="card">Card</option>
                  <option value="mobile">Mobile</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Status
                </label>
                <select
                  value={updateFormData.status}
                  onChange={(e) => setUpdateFormData(prev => ({ ...prev, status: e.target.value as 'pending' | 'completed' | 'cancelled' | 'refunded' }))}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="pending">Pending</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                  <option value="refunded">Refunded</option>
                </select>
              </div>

              <div className="flex space-x-3 pt-4">
                <Button onClick={handleUpdateOrder} className="flex-1">
                  Update Order
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowUpdateForm(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrdersManagement;
